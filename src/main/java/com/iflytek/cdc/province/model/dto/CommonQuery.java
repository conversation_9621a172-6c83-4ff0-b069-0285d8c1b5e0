package com.iflytek.cdc.province.model.dto;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CommonQuery {
    @ApiModelProperty(
            value = "页码 从1开始",
            example = "1"
    )
    public Integer pageIndex;
    @ApiModelProperty(
            value = "每页条数",
            example = "10"
    )
    public Integer pageSize;
    @ApiModelProperty( "所在省")
    private String provinceCode;
    @ApiModelProperty( "所在市")
    private String cityCode;
    @ApiModelProperty( "所在县区")
    private String districtCode;

    @ApiModelProperty( "所在省多选")
    private List<String> provinceCodeList;
    @ApiModelProperty( "所在市多选")
    private List<String> cityCodeList;
    @ApiModelProperty( "所在县区多选")
    private List<String> districtCodeList;

    @ApiModelProperty( "所在街道多选")
    private List<String> streetCodeList;

    @ApiModelProperty( "所在街道")
    private String streetCode;

    @ApiModelProperty("关键词")
    private String queryKey;

    @ApiModelProperty( "排序字段")
    private String property;
    @ApiModelProperty( "排序顺序")
    private String direction;

    private List<String> provinceCodes;
    private List<String> cityCodes;
    private List<String> districtCodes;
    private List<String> streetCodes;
    private Integer areaLevel;
    private String areaCode;

    @ApiModelProperty("开始时间")
    private Date startDate;
    @ApiModelProperty("结束日期")
    private Date endDate;

    @ApiModelProperty("上期开始时间")
    private Date lastStartDate;
    @ApiModelProperty("上期结束日期")
    private Date lastEndDate;

    @ApiModelProperty("同期开始时间")
    private Date lastYStartDate;
    @ApiModelProperty("同期结束日期")
    private Date lastYEndDate;

    @ApiModelProperty("类型")
    private String processType;

    private Boolean userDiseasePermissionFlag;

    private List<String> diseasePermissionCodeList;


    public void dealDate(CommonQuery queryParam){
        long offset = queryParam.getEndDate().getTime() - queryParam.getStartDate().getTime() + 1;
        DateTime lastYStartDate = DateUtil.offset(queryParam.getStartDate(), DateField.YEAR, -1);
        DateTime lastYEndDate = DateUtil.offset(queryParam.getEndDate(), DateField.YEAR, -1);
        queryParam.setLastStartDate(new Date(queryParam.getStartDate().getTime() - offset));
        queryParam.setLastEndDate(new Date(queryParam.getEndDate().getTime() - offset));
        queryParam.setLastYStartDate(lastYStartDate);
        queryParam.setLastYEndDate(lastYEndDate);
    }

}
