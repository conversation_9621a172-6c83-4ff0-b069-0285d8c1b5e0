package com.iflytek.cdc.province.service.impl;

import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.province.model.dto.CommonQuery;
import com.iflytek.cdc.province.model.dto.dm.DataSearchCommonQuery;
import com.iflytek.cdc.province.service.DiseasePermissionService;
import com.iflytek.cdc.province.vo.PermissionDiseaseInfoDataVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DiseasePermissionServiceImpl implements DiseasePermissionService {

    private CdcAdminServiceApi cdcAdminServiceApi;

    @Autowired
    public void setCdcAdminServiceApi(CdcAdminServiceApi cdcAdminServiceApi) {
        this.cdcAdminServiceApi = cdcAdminServiceApi;
    }

    @Override
    public PermissionDiseaseInfoDataVO queryUserDiseasePermission(String loginUserId, String moduleType) {

        return cdcAdminServiceApi.queryUserDiseasePermission(loginUserId, moduleType);
    }

    public <T extends CommonQuery> void checkUserDiseasePermission(T dto, String loginUserId){

    }
}
